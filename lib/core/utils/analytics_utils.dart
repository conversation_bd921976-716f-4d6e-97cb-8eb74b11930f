import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/room_category.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/room_info.dart';
import 'package:flutter_audio_room/features/authentication/data/datasource/auth_remote_data_source.dart';

/// Firebase Analytics utility class for tracking user events and behaviors
class AnalyticsUtils {
  AnalyticsUtils._();

  static final FirebaseAnalytics _analytics = FirebaseAnalytics.instance;
  static const String _tag = 'AnalyticsUtils';

  /// Initialize Firebase Analytics
  /// Note: For iOS tracking permissions, use TrackingPermissionUtils.initializeTracking() instead
  static Future<void> initialize() async {
    try {
      // In debug mode, disable analytics collection
      // In production, enable by default (iOS permissions will be handled separately)
      await _analytics.setAnalyticsCollectionEnabled(!kDebugMode);
      LogUtils.d('Firebase Analytics initialized (debug mode: $kDebugMode)', tag: _tag);
    } catch (e) {
      LogUtils.e('Failed to initialize Firebase Analytics: $e', tag: _tag);
    }
  }

  /// Set user properties
  static Future<void> setUserProperties({
    required String userId,
    String? username,
    String? userLevel,
    String? country,
    String? language,
  }) async {
    try {
      await _analytics.setUserId(id: userId);
      
      if (username != null) {
        await _analytics.setUserProperty(name: 'username', value: username);
      }
      if (userLevel != null) {
        await _analytics.setUserProperty(name: 'user_level', value: userLevel);
      }
      if (country != null) {
        await _analytics.setUserProperty(name: 'country', value: country);
      }
      if (language != null) {
        await _analytics.setUserProperty(name: 'language', value: language);
      }
      
      LogUtils.d('User properties set for user: $userId', tag: _tag);
    } catch (e) {
      LogUtils.e('Failed to set user properties: $e', tag: _tag);
    }
  }

  /// Clear user data (for logout)
  static Future<void> clearUserData() async {
    try {
      await _analytics.setUserId(id: null);
      LogUtils.d('User data cleared', tag: _tag);
    } catch (e) {
      LogUtils.e('Failed to clear user data: $e', tag: _tag);
    }
  }

  // ==================== Authentication Events ====================

  /// Track user signup event
  static Future<void> trackSignup({
    required AuthType authType,
    required String method,
    bool? success,
    String? errorMessage,
  }) async {
    try {
      await _analytics.logSignUp(signUpMethod: method);
      
      await _analytics.logEvent(
        name: 'user_signup',
        parameters: {
          'auth_type': authType.name,
          'method': method,
          'success': success ?? true,
          if (errorMessage != null) 'error_message': errorMessage,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );
      
      LogUtils.d('Signup event tracked: $method', tag: _tag);
    } catch (e) {
      LogUtils.e('Failed to track signup event: $e', tag: _tag);
    }
  }

  /// Track user login event
  static Future<void> trackLogin({
    required AuthType authType,
    required String method,
    bool? success,
    String? errorMessage,
  }) async {
    try {
      await _analytics.logLogin(loginMethod: method);
      
      await _analytics.logEvent(
        name: 'user_login',
        parameters: {
          'auth_type': authType.name,
          'method': method,
          'success': success ?? true,
          if (errorMessage != null) 'error_message': errorMessage,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );
      
      LogUtils.d('Login event tracked: $method', tag: _tag);
    } catch (e) {
      LogUtils.e('Failed to track login event: $e', tag: _tag);
    }
  }

  /// Track user logout event
  static Future<void> trackLogout({
    String? reason,
  }) async {
    try {
      await _analytics.logEvent(
        name: 'user_logout',
        parameters: {
          if (reason != null) 'reason': reason,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );
      
      LogUtils.d('Logout event tracked', tag: _tag);
    } catch (e) {
      LogUtils.e('Failed to track logout event: $e', tag: _tag);
    }
  }

  // ==================== Audio Room Events ====================

  /// Track room creation event
  static Future<void> trackRoomCreated({
    required String roomId,
    required String roomTitle,
    required RoomCategory category,
    required RoomType roomType,
    bool? success,
    String? errorMessage,
  }) async {
    try {
      await _analytics.logEvent(
        name: 'room_created',
        parameters: {
          'room_id': roomId,
          'room_title': roomTitle,
          'category': category.name,
          'room_type': roomType.name,
          'success': success ?? true,
          if (errorMessage != null) 'error_message': errorMessage,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );
      
      LogUtils.d('Room created event tracked: $roomId', tag: _tag);
    } catch (e) {
      LogUtils.e('Failed to track room created event: $e', tag: _tag);
    }
  }

  /// Track room join event
  static Future<void> trackRoomJoined({
    required String roomId,
    required String joinSource,
    String? roomTitle,
    RoomCategory? category,
    int? memberCount,
    bool? success,
    String? errorMessage,
  }) async {
    try {
      await _analytics.logJoinGroup(
        groupId: roomId,
      );
      
      await _analytics.logEvent(
        name: 'room_joined',
        parameters: {
          'room_id': roomId,
          'join_source': joinSource,
          if (roomTitle != null) 'room_title': roomTitle,
          if (category != null) 'category': category.name,
          if (memberCount != null) 'member_count': memberCount,
          'success': success ?? true,
          if (errorMessage != null) 'error_message': errorMessage,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );
      
      LogUtils.d('Room joined event tracked: $roomId', tag: _tag);
    } catch (e) {
      LogUtils.e('Failed to track room joined event: $e', tag: _tag);
    }
  }

  /// Track room leave event
  static Future<void> trackRoomLeft({
    required String roomId,
    String? reason,
    int? durationSeconds,
    bool? wasCreator,
  }) async {
    try {
      await _analytics.logEvent(
        name: 'room_left',
        parameters: {
          'room_id': roomId,
          if (reason != null) 'reason': reason,
          if (durationSeconds != null) 'duration_seconds': durationSeconds,
          if (wasCreator != null) 'was_creator': wasCreator,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );
      
      LogUtils.d('Room left event tracked: $roomId', tag: _tag);
    } catch (e) {
      LogUtils.e('Failed to track room left event: $e', tag: _tag);
    }
  }

  /// Track room ended event
  static Future<void> trackRoomEnded({
    required String roomId,
    int? durationSeconds,
    int? maxMemberCount,
    String? reason,
  }) async {
    try {
      await _analytics.logEvent(
        name: 'room_ended',
        parameters: {
          'room_id': roomId,
          if (durationSeconds != null) 'duration_seconds': durationSeconds,
          if (maxMemberCount != null) 'max_member_count': maxMemberCount,
          if (reason != null) 'reason': reason,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );
      
      LogUtils.d('Room ended event tracked: $roomId', tag: _tag);
    } catch (e) {
      LogUtils.e('Failed to track room ended event: $e', tag: _tag);
    }
  }

  // ==================== User Interaction Events ====================

  /// Track mic on/off events
  static Future<void> trackMicAction({
    required String roomId,
    required String action, // 'take_mic', 'drop_mic', 'mute', 'unmute'
    int? seatPosition,
    bool? success,
  }) async {
    try {
      await _analytics.logEvent(
        name: 'mic_action',
        parameters: {
          'room_id': roomId,
          'action': action,
          if (seatPosition != null) 'seat_position': seatPosition,
          'success': success ?? true,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );
      
      LogUtils.d('Mic action tracked: $action in room $roomId', tag: _tag);
    } catch (e) {
      LogUtils.e('Failed to track mic action: $e', tag: _tag);
    }
  }

  /// Track message sent event
  static Future<void> trackMessageSent({
    String? roomId,
    String? conversationId,
    required String
        messageType, // 'text', 'image', 'audio', 'video', 'gift', etc.
    int? messageLength,
    String? context, // 'room_chat', 'private_chat', 'group_chat'
  }) async {
    try {
      await _analytics.logEvent(
        name: 'message_sent',
        parameters: {
          if (roomId != null) 'room_id': roomId,
          if (conversationId != null) 'conversation_id': conversationId,
          'message_type': messageType,
          if (messageLength != null) 'message_length': messageLength,
          if (context != null) 'context': context,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );

      final location =
          roomId != null ? 'room $roomId' : 'conversation $conversationId';
      LogUtils.d('Message sent tracked: $messageType in $location', tag: _tag);
    } catch (e) {
      LogUtils.e('Failed to track message sent: $e', tag: _tag);
    }
  }

  // ==================== App Lifecycle Events ====================

  /// Track app launch event
  static Future<void> trackAppLaunch({
    String? launchSource,
    bool? isFirstLaunch,
  }) async {
    try {
      await _analytics.logAppOpen();

      await _analytics.logEvent(
        name: 'app_launch',
        parameters: {
          if (launchSource != null) 'launch_source': launchSource,
          if (isFirstLaunch != null) 'is_first_launch': isFirstLaunch,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );

      LogUtils.d('App launch tracked', tag: _tag);
    } catch (e) {
      LogUtils.e('Failed to track app launch: $e', tag: _tag);
    }
  }

  /// Track app background/foreground events
  static Future<void> trackAppStateChange({
    required String state, // 'background', 'foreground'
    int? sessionDuration,
  }) async {
    try {
      await _analytics.logEvent(
        name: 'app_state_change',
        parameters: {
          'state': state,
          if (sessionDuration != null) 'session_duration': sessionDuration,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );

      LogUtils.d('App state change tracked: $state', tag: _tag);
    } catch (e) {
      LogUtils.e('Failed to track app state change: $e', tag: _tag);
    }
  }

  // ==================== Feature Usage Events ====================

  /// Track screen view events
  static Future<void> trackScreenView({
    required String screenName,
    String? screenClass,
    Map<String, Object>? parameters,
  }) async {
    try {
      await _analytics.logScreenView(
        screenName: screenName,
        screenClass: screenClass,
      );

      if (parameters != null) {
        await _analytics.logEvent(
          name: 'screen_view_detailed',
          parameters: {
            'screen_name': screenName,
            if (screenClass != null) 'screen_class': screenClass,
            ...parameters,
            'timestamp': DateTime.now().millisecondsSinceEpoch,
          },
        );
      }

      LogUtils.d('Screen view tracked: $screenName', tag: _tag);
    } catch (e) {
      LogUtils.e('Failed to track screen view: $e', tag: _tag);
    }
  }

  /// Track button click events
  static Future<void> trackButtonClick({
    required String buttonName,
    required String screenName,
    Map<String, Object>? additionalParams,
  }) async {
    try {
      await _analytics.logEvent(
        name: 'button_click',
        parameters: {
          'button_name': buttonName,
          'screen_name': screenName,
          if (additionalParams != null) ...additionalParams,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );

      LogUtils.d('Button click tracked: $buttonName on $screenName', tag: _tag);
    } catch (e) {
      LogUtils.e('Failed to track button click: $e', tag: _tag);
    }
  }

  /// Track search events
  static Future<void> trackSearch({
    required String searchTerm,
    String? searchCategory,
    int? resultCount,
  }) async {
    try {
      await _analytics.logSearch(
        searchTerm: searchTerm,
      );

      await _analytics.logEvent(
        name: 'search_performed',
        parameters: {
          'search_term': searchTerm,
          if (searchCategory != null) 'search_category': searchCategory,
          if (resultCount != null) 'result_count': resultCount,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );

      LogUtils.d('Search tracked: $searchTerm', tag: _tag);
    } catch (e) {
      LogUtils.e('Failed to track search: $e', tag: _tag);
    }
  }

  // ==================== Error and Performance Events ====================

  /// Track error events
  static Future<void> trackError({
    required String errorType,
    required String errorMessage,
    String? errorCode,
    String? stackTrace,
    String? context,
  }) async {
    try {
      await _analytics.logEvent(
        name: 'app_error',
        parameters: {
          'error_type': errorType,
          'error_message': errorMessage,
          if (errorCode != null) 'error_code': errorCode,
          if (stackTrace != null) 'stack_trace': stackTrace,
          if (context != null) 'context': context,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );

      LogUtils.d('Error tracked: $errorType - $errorMessage', tag: _tag);
    } catch (e) {
      LogUtils.e('Failed to track error: $e', tag: _tag);
    }
  }

  /// Track performance events
  static Future<void> trackPerformance({
    required String operationName,
    required int durationMs,
    bool? success,
    String? additionalInfo,
  }) async {
    try {
      await _analytics.logEvent(
        name: 'performance_metric',
        parameters: {
          'operation_name': operationName,
          'duration_ms': durationMs,
          'success': success ?? true,
          if (additionalInfo != null) 'additional_info': additionalInfo,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );

      LogUtils.d('Performance tracked: $operationName took ${durationMs}ms', tag: _tag);
    } catch (e) {
      LogUtils.e('Failed to track performance: $e', tag: _tag);
    }
  }

  // ==================== Purchase Events ====================

  /// Track purchase events using Firebase's built-in logPurchase
  static Future<void> trackPurchase({
    required String currency,
    required double value,
    required String transactionId,
    String? itemId,
    String? itemName,
    String? itemCategory,
    int? quantity,
    Map<String, Object>? additionalParams,
  }) async {
    try {
      await _analytics.logPurchase(
        currency: currency,
        value: value,
        transactionId: transactionId,
        parameters: {
          if (itemId != null) 'item_id': itemId,
          if (itemName != null) 'item_name': itemName,
          if (itemCategory != null) 'item_category': itemCategory,
          if (quantity != null) 'quantity': quantity,
          if (additionalParams != null) ...additionalParams,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );

      LogUtils.d('Purchase tracked: $itemName ($value $currency)', tag: _tag);
    } catch (e) {
      LogUtils.e('Failed to track purchase: $e', tag: _tag);
    }
  }

  /// Track subscription purchase
  static Future<void> trackSubscriptionPurchase({
    required String subscriptionType, // 'premium', 'vip', etc.
    required String duration, // 'monthly', 'yearly', etc.
    required double price,
    required String currency,
    required String transactionId,
  }) async {
    try {
      await _analytics.logPurchase(
        currency: currency,
        value: price,
        transactionId: transactionId,
        parameters: {
          'item_category': 'subscription',
          'subscription_type': subscriptionType,
          'subscription_duration': duration,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );

      LogUtils.d('Subscription purchase tracked: $subscriptionType ($price $currency)', tag: _tag);
    } catch (e) {
      LogUtils.e('Failed to track subscription purchase: $e', tag: _tag);
    }
  }

  /// Track virtual item purchase (gifts, coins, etc.)
  static Future<void> trackVirtualItemPurchase({
    required String itemType, // 'gift', 'coins', 'diamonds', etc.
    required String itemName,
    required int quantity,
    required double price,
    required String currency,
    required String transactionId,
    String? recipientId,
  }) async {
    try {
      await _analytics.logPurchase(
        currency: currency,
        value: price,
        transactionId: transactionId,
        parameters: {
          'item_category': 'virtual_item',
          'item_type': itemType,
          'item_name': itemName,
          'quantity': quantity,
          if (recipientId != null) 'recipient_id': recipientId,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );

      LogUtils.d('Virtual item purchase tracked: $itemName x$quantity ($price $currency)', tag: _tag);
    } catch (e) {
      LogUtils.e('Failed to track virtual item purchase: $e', tag: _tag);
    }
  }

  // ==================== Custom Events ====================

  /// Track custom events with flexible parameters
  static Future<void> trackCustomEvent({
    required String eventName,
    Map<String, Object>? parameters,
  }) async {
    try {
      final eventParams = <String, Object>{
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        if (parameters != null) ...parameters,
      };

      await _analytics.logEvent(
        name: eventName,
        parameters: eventParams,
      );

      LogUtils.d('Custom event tracked: $eventName', tag: _tag);
    } catch (e) {
      LogUtils.e('Failed to track custom event: $e', tag: _tag);
    }
  }

  /// Track user engagement events
  static Future<void> trackUserEngagement({
    required String engagementType, // 'daily_active', 'weekly_active', 'feature_used'
    String? featureName,
    int? engagementValue,
  }) async {
    try {
      await _analytics.logEvent(
        name: 'user_engagement',
        parameters: {
          'engagement_type': engagementType,
          if (featureName != null) 'feature_name': featureName,
          if (engagementValue != null) 'engagement_value': engagementValue,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );

      LogUtils.d('User engagement tracked: $engagementType', tag: _tag);
    } catch (e) {
      LogUtils.e('Failed to track user engagement: $e', tag: _tag);
    }
  }

  // ==================== Utility Methods ====================

  /// Get Firebase Analytics instance
  static FirebaseAnalytics get instance => _analytics;

  /// Enable/disable analytics collection
  static Future<void> setAnalyticsEnabled(bool enabled) async {
    try {
      await _analytics.setAnalyticsCollectionEnabled(enabled);
      LogUtils.d('Analytics collection ${enabled ? 'enabled' : 'disabled'}', tag: _tag);
    } catch (e) {
      LogUtils.e('Failed to set analytics enabled: $e', tag: _tag);
    }
  }

  /// Reset analytics data
  static Future<void> resetAnalyticsData() async {
    try {
      await _analytics.resetAnalyticsData();
      LogUtils.d('Analytics data reset', tag: _tag);
    } catch (e) {
      LogUtils.e('Failed to reset analytics data: $e', tag: _tag);
    }
  }
}
